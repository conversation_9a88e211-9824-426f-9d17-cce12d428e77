{"name": "@mem0/vercel-ai-provider", "version": "1.0.7", "description": "Vercel AI Provider for providing memory to LLMs", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**/*"], "scripts": {"build": "tsup", "clean": "rm -rf dist", "dev": "nodemon", "lint": "eslint \"./**/*.ts*\"", "type-check": "tsc --noEmit", "prettier-check": "prettier --check \"./**/*.ts*\"", "test": "jest", "test:edge": "vitest --config vitest.edge.config.js --run", "test:node": "vitest --config vitest.node.config.js --run"}, "keywords": ["ai", "vercel-ai"], "author": "Saket Aryan <<EMAIL>>", "license": "Apache-2.0", "dependencies": {"@ai-sdk/anthropic": "1.1.12", "@ai-sdk/cohere": "1.1.12", "@ai-sdk/google": "1.2.18", "@ai-sdk/groq": "1.1.11", "@ai-sdk/openai": "1.1.15", "@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10", "ai": "4.1.46", "dotenv": "^16.4.5", "mem0ai": "^2.1.12", "partial-json": "0.1.7", "zod": "^3.0.0"}, "devDependencies": {"@edge-runtime/vm": "^3.2.0", "@types/jest": "^29.5.14", "@types/node": "^18.19.46", "jest": "^29.7.0", "nodemon": "^3.1.7", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsup": "^8.3.0", "typescript": "^5.5.4"}, "peerDependencies": {"zod": "^3.0.0"}, "peerDependenciesMeta": {"zod": {"optional": true}}, "engines": {"node": ">=18"}, "publishConfig": {"access": "public"}, "directories": {"example": "example", "test": "tests"}, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b", "pnpm": {"onlyBuiltDependencies": ["esbuild", "sqlite3"]}}