{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Memgraph as Graph Memory"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prerequisites\n", "\n", "### 1. Install Mem0 with Graph Memory support \n", "\n", "To use Mem0 with Graph Memory support, install it using pip:\n", "\n", "```bash\n", "pip install \"mem0ai[graph]\"\n", "```\n", "\n", "This command installs Mem0 along with the necessary dependencies for graph functionality.\n", "\n", "### 2. Install Memgraph\n", "\n", "To utilize Memgraph as Graph Memory, run it with Docker:\n", "\n", "```bash\n", "docker run -p 7687:7687 memgraph/memgraph-mage:latest --schema-info-enabled=True\n", "```\n", "\n", "The `--schema-info-enabled` flag is set to `True` for more performant schema\n", "generation.\n", "\n", "Additional information can be found on [Memgraph documentation](https://memgraph.com/docs). "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration\n", "\n", "Do all the imports and configure OpenAI (enter your OpenAI API key):"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from mem0 import Memory\n", "\n", "import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Set up configuration to use the embedder model and Memgraph as a graph store:"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["config = {\n", "    \"embedder\": {\n", "        \"provider\": \"openai\",\n", "        \"config\": {\"model\": \"text-embedding-3-large\", \"embedding_dims\": 1536},\n", "    },\n", "    \"graph_store\": {\n", "        \"provider\": \"memgraph\",\n", "        \"config\": {\n", "            \"url\": \"bolt://localhost:7687\",\n", "            \"username\": \"memgraph\",\n", "            \"password\": \"mem0graph\",\n", "        },\n", "    },\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Graph Memory initializiation \n", "\n", "Initialize Memgraph as a Graph Memory store: "]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/repos/forks/mem0/.venv/lib/python3.13/site-packages/neo4j/_sync/driver.py:547: DeprecationWarning: Relying on Driver's destructor to close the session is deprecated. Please make sure to close the session. Use it as a context (`with` statement) or make sure to call `.close()` explicitly. Future versions of the driver will not close drivers automatically.\n", "  _deprecation_warn(\n"]}], "source": ["m = Memory.from_config(config_dict=config)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Store memories \n", "\n", "Create memories:"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": \"I'm planning to watch a movie tonight. Any recommendations?\",\n", "    },\n", "    {\n", "        \"role\": \"assistant\",\n", "        \"content\": \"How about a thriller movies? They can be quite engaging.\",\n", "    },\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": \"I'm not a big fan of thriller movies but I love sci-fi movies.\",\n", "    },\n", "    {\n", "        \"role\": \"assistant\",\n", "        \"content\": \"Got it! I'll avoid thriller recommendations and suggest sci-fi movies in the future.\",\n", "    },\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Store memories in Memgraph:"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# Store inferred memories (default behavior)\n", "result = m.add(messages, user_id=\"alice\", metadata={\"category\": \"movie_recommendations\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](./alice-memories.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Search memories"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loves sci-fi movies 0.31536642873408993\n", "Planning to watch a movie tonight 0.09684523796547778\n", "Not a big fan of thriller movies 0.09468540071789475\n"]}], "source": ["for result in m.search(\"what does alice love?\", user_id=\"alice\")[\"results\"]:\n", "    print(result[\"memory\"], result[\"score\"])"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}