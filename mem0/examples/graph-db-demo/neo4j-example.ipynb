{"cells": [{"cell_type": "markdown", "metadata": {"id": "ApdaLD4Qi30H"}, "source": ["# Neo4j as Graph Memory"]}, {"cell_type": "markdown", "metadata": {"id": "l7bi3i21i30I"}, "source": ["## Prerequisites\n", "\n", "### 1. Install Mem0 with Graph Memory support\n", "\n", "To use Mem0 with Graph Memory support, install it using pip:\n", "\n", "```bash\n", "pip install \"mem0ai[graph]\"\n", "```\n", "\n", "This command installs Mem0 along with the necessary dependencies for graph functionality.\n", "\n", "### 2. Install Neo4j\n", "\n", "To utilize Neo4j as Graph Memory, run it with Docker:\n", "\n", "```bash\n", "docker run \\\n", "  -p 7474:7474 -p 7687:7687 \\\n", "  -e NEO4J_AUTH=neo4j/password \\\n", "  neo4j:5\n", "```\n", "\n", "This command starts Neo4j with default credentials (`neo4j` / `password`) and exposes both the HTTP (7474) and Bolt (7687) ports.\n", "\n", "You can access the Neo4j browser at [http://localhost:7474](http://localhost:7474).\n", "\n", "Additional information can be found in the [Neo4j documentation](https://neo4j.com/docs/).\n"]}, {"cell_type": "markdown", "metadata": {"id": "DkeBdFEpi30I"}, "source": ["## Configuration\n", "\n", "Do all the imports and configure OpenAI (enter your OpenAI API key):"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "d99EfBpii30I"}, "outputs": [], "source": ["from mem0 import Memory\n", "\n", "import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"\""]}, {"cell_type": "markdown", "metadata": {"id": "QTucZJjIi30J"}, "source": ["Set up configuration to use the embedder model and Neo4j as a graph store:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "QSE0RFoSi30J"}, "outputs": [], "source": ["config = {\n", "    \"embedder\": {\n", "        \"provider\": \"openai\",\n", "        \"config\": {\"model\": \"text-embedding-3-large\", \"embedding_dims\": 1536},\n", "    },\n", "    \"graph_store\": {\n", "        \"provider\": \"neo4j\",\n", "        \"config\": {\n", "            \"url\": \"bolt://54.87.227.131:7687\",\n", "            \"username\": \"neo4j\",\n", "            \"password\": \"causes-bins-vines\",\n", "        },\n", "    },\n", "}"]}, {"cell_type": "markdown", "metadata": {"id": "OioTnv6xi30J"}, "source": ["## Graph Memory initializiation\n", "\n", "Initialize Neo4j as a Graph Memory store:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "fX-H9vgNi30J"}, "outputs": [], "source": ["m = Memory.from_config(config_dict=config)"]}, {"cell_type": "markdown", "metadata": {"id": "kr1fVMwEi30J"}, "source": ["## Store memories\n", "\n", "Create memories:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "sEfogqp_i30J"}, "outputs": [], "source": ["messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": \"I'm planning to watch a movie tonight. Any recommendations?\",\n", "    },\n", "    {\n", "        \"role\": \"assistant\",\n", "        \"content\": \"How about a thriller movies? They can be quite engaging.\",\n", "    },\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": \"I'm not a big fan of thriller movies but I love sci-fi movies.\",\n", "    },\n", "    {\n", "        \"role\": \"assistant\",\n", "        \"content\": \"Got it! I'll avoid thriller recommendations and suggest sci-fi movies in the future.\",\n", "    },\n", "]"]}, {"cell_type": "markdown", "metadata": {"id": "gtBHCyIgi30J"}, "source": ["Store memories in Neo4j:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "BMVGgZMFi30K"}, "outputs": [], "source": ["# Store inferred memories (default behavior)\n", "result = m.add(messages, user_id=\"alice\")"]}, {"cell_type": "markdown", "metadata": {"id": "lQRptOywi30K"}, "source": ["![](https://github.com/tomasonjo/mem0/blob/neo4jexample/examples/graph-db-demo/alice-memories.png?raw=1)"]}, {"cell_type": "markdown", "metadata": {"id": "LBXW7Gv-i30K"}, "source": ["## Search memories"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UHFDeQBEi30K", "outputId": "2c69de7d-a79a-48f6-e3c4-bd743067857c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loves sci-fi movies 0.3153664287340898\n", "Planning to watch a movie tonight 0.09683349296551162\n", "Not a big fan of thriller movies 0.09468540071789466\n"]}], "source": ["for result in m.search(\"what does alice love?\", user_id=\"alice\")[\"results\"]:\n", "    print(result[\"memory\"], result[\"score\"])"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "2jXEIma9kK_Q"}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 0}