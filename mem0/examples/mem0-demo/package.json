{"name": "mem0-demo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.1.15", "@assistant-ui/react": "^0.8.2", "@assistant-ui/react-ai-sdk": "^0.8.0", "@assistant-ui/react-markdown": "^0.8.0", "@mem0/vercel-ai-provider": "^1.0.4", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@types/js-cookie": "^3.0.6", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "ai": "^4.1.46", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "js-cookie": "^3.0.5", "lucide-react": "^0.477.0", "next": "15.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.0.1", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "packageManager": "pnpm@10.5.2", "pnpm": {"onlyBuiltDependencies": ["sqlite3"]}}