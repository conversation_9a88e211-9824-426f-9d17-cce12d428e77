FROM python:3.12-slim-bookworm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    python3-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy all files first
COPY . .

# Install Poetry
RUN curl -sSL https://install.python-poetry.org | python3 -
ENV PATH="/root/.local/bin:$PATH"

# Install mem0 in editable mode with graph dependencies
RUN cd /app && pip install -e .[graph]

# Install server requirements
RUN pip install -r server/requirements.txt

# Install additional dependencies for Ollama integration
RUN pip install psycopg2-binary ollama

# Set environment variables
ENV PYTHONPATH="/app:/app/server:/app/packages:${PYTHONPATH}"
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV OLLAMA_BASE_URL="http://ollama:11434"
ENV LLM_PROVIDER="ollama"

# Switch to server directory
WORKDIR /app/server

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
