FROM python:3.12-slim-bookworm

# --- 加速配置 ---
RUN sed -i 's/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list.d/debian.sources
RUN pip3 config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip3 config set global.timeout 600

# --- 安装系统依赖 ---
RUN apt-get update && apt-get install -y gcc python3-dev && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# --- 复制所有文件 ---
COPY . .

# --- 先安装 mem0 包，再安装其他依赖 ---
RUN pip3 install --no-cache-dir --force-reinstall -e .[graph]
RUN pip3 install -r server/requirements.txt
RUN pip3 install psycopg2-binary ollama

# --- 设置环境变量 ---
ENV PYTHONPATH="/app:/app/server:${PYTHONPATH}"
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV OLLAMA_BASE_URL="http://ollama:11434"
ENV LLM_PROVIDER="ollama"

# --- 切换到 server 目录并直接运行 main.py ---
WORKDIR /app/server
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
