# 使用一个明确的 Debian 版本作为基础镜像，方便我们换源
FROM python:3.12-slim-bookworm

# --- 全方位中国加速配置 (清华源) ---

# 1. APT 源加速 (操作系统包)
# 更换为清华大学的 Debian 源，以加速未来可能的 apt-get install 命令
RUN sed -i 's/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list.d/debian.sources

# 2. PIP 源加速 (Python 包)
# 永久性地将 pip 的源更换为清华大学镜像源
RUN pip3 config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip3 config set global.timeout 600

# --- 加速配置结束 ---


# --- 原有的 Dockerfile 逻辑 ---

# 设置工作目录
WORKDIR /app

# 复制依赖文件
# 最好将 requirements.txt 单独复制，这样可以利用 Docker 的层缓存
# 只有当 a.txt 变化时，下面的 RUN 才会重新执行
COPY requirements.txt .

# 使用我们加速过的 pip 来安装依赖
# 增加了 --no-cache-dir 来减小镜像体积
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目的所有其他文件
COPY . .

# 暴露端口
EXPOSE 8000

# 设置环境变量，确保 Python 输出能立即被 Docker logs 捕获
ENV PYTHONUNBUFFERED=1

# 容器启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]