name: mem0-dev

services:
  mem0:
    build:
      context: ..
      dockerfile: server/dev.Dockerfile
    ports:
      - "8888:8000"
    env_file:
      - .env
    networks:
      - mem0_network
    volumes:
      - ./history:/app/history
      - .:/app
      - ../mem0:/app/packages/mem0
    depends_on:
      postgres:
        condition: service_healthy
      neo4j:
        condition: service_healthy
      ollama:
        condition: service_started
   
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1

  postgres:
    image: ankane/pgvector:v0.5.1
    restart: on-failure
    shm_size: "128mb"
    networks:
      - mem0_network
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    healthcheck:
      test: ["CMD", "pg_isready", "-q", "-d", "postgres", "-U", "postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - postgres_db:/var/lib/postgresql/data
    ports:
      - "8432:5432"

  neo4j:
    image: neo4j:5.26.4
    networks:
      - mem0_network
    healthcheck:
      test: wget http://localhost:7687 || exit 1
      interval: 1s
      timeout: 10s
      retries: 20
      start_period: 3s
    ports:
      - "8474:7474" # HTTP
      - "8687:7687" # Bolt
    volumes:
      - neo4j_data:/data
    environment:
      - NEO4J_AUTH=neo4j/mem0graph
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true

  # --- 这是我们新加的 Ollama 服务 ---
  # 注意它的缩进，和 mem0, postgres, neo4j 是平级的
  ollama:
    image: ollama/ollama:latest
    container_name: mem0_ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - mem0_network

# --- 这是顶级的、唯一的 volumes 和 networks 定义 ---
volumes:
  postgres_db:
  neo4j_data:
  ollama_data: # <--- ollama_data 在这里声明

networks:
  mem0_network:
    driver: bridge
